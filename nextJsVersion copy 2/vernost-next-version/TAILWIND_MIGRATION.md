# 🎨 Tailwind CSS Migration Guide

Your design system has been successfully converted from custom CSS to **Tailwind CSS**! This guide will help you understand the changes and how to use the new system.

## 🚀 What Changed

### ✅ **Before (Custom CSS)**
```css
.btn-primary {
  background-color: var(--primary-500);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
}
```

### ✅ **After (Tailwind CSS)**
```html
<button class="bg-primary-500 text-white px-6 py-3 rounded-lg">
  Primary Button
</button>
```

## 🎯 **Key Benefits**

1. **Smaller Bundle Size**: Only the CSS you use gets included
2. **Better Performance**: Optimized CSS output
3. **Consistency**: Standardized design tokens
4. **Developer Experience**: IntelliSense support and better tooling
5. **Maintainability**: No more custom CSS to maintain

## 🎨 **Design System Overview**

### **Colors**
Your GoIndigo brand colors are preserved:

```html
<!-- Primary Colors -->
<div class="bg-primary-500 text-white">Main Brand Blue</div>
<div class="bg-primary-50 text-primary-700">Light Blue Background</div>

<!-- Secondary Colors -->
<div class="bg-secondary-500 text-white">Brand Orange</div>
<div class="bg-secondary-50 text-secondary-700">Light Orange Background</div>

<!-- Semantic Colors -->
<div class="bg-success-500 text-white">Success</div>
<div class="bg-error-500 text-white">Error</div>
<div class="bg-warning-500 text-white">Warning</div>
<div class="bg-info-500 text-white">Info</div>
```

### **Typography**
Responsive typography with your custom fonts:

```html
<!-- Display Headings (Bauhaus Pro) -->
<h1 class="text-3xl md:text-4xl lg:text-5xl font-display font-medium">
  Main Heading
</h1>

<!-- Content Headings (Poppins) -->
<h3 class="text-xl md:text-2xl lg:text-3xl font-semibold">
  Section Heading
</h3>

<!-- Body Text -->
<p class="text-base leading-relaxed text-neutral-700">
  Body text content
</p>
```

### **Buttons**
Use the custom button components or build with utilities:

```html
<!-- Custom Button Components (Recommended) -->
<button class="btn btn-primary">Primary Button</button>
<button class="btn btn-secondary">Secondary Button</button>
<button class="btn btn-outline">Outline Button</button>
<button class="btn btn-ghost">Ghost Button</button>

<!-- Button Sizes -->
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary btn-lg">Large</button>

<!-- Or Build with Utilities -->
<button class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg transition-all">
  Custom Button
</button>
```

### **Layout & Spacing**
Consistent spacing system:

```html
<!-- Container -->
<div class="container mx-auto px-4">Content</div>

<!-- Flexbox -->
<div class="flex items-center justify-between gap-4">
  <div>Item 1</div>
  <div>Item 2</div>
</div>

<!-- Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <div>Card 1</div>
  <div>Card 2</div>
  <div>Card 3</div>
</div>

<!-- Spacing -->
<div class="p-6 m-4 space-y-4">
  <p>Paragraph 1</p>
  <p>Paragraph 2</p>
</div>
```

## 🔄 **Migration Examples**

### **Cards**
```html
<!-- Old Custom CSS -->
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Title</h3>
  </div>
  <div class="card-body">Content</div>
</div>

<!-- New Tailwind CSS -->
<div class="bg-white rounded-xl shadow-base p-6 border border-neutral-100 transition-all hover:shadow-lg hover:-translate-y-0.5">
  <h3 class="text-xl font-semibold text-neutral-800 mb-2">Title</h3>
  <p class="text-neutral-600">Content</p>
</div>
```

### **Forms**
```html
<!-- Input Fields -->
<input class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none focus:bg-primary-50 transition-all" />

<!-- Form Groups -->
<div class="mb-6">
  <label class="block font-medium text-neutral-700 mb-2 text-sm">Label</label>
  <input class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none transition-all" />
  <span class="text-error-500 text-sm mt-1 block">Error message</span>
</div>
```

### **Badges & Alerts**
```html
<!-- Badges -->
<span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full uppercase tracking-wide bg-primary-100 text-primary-700">
  Badge
</span>

<!-- Alerts -->
<div class="p-4 rounded-lg border bg-success-50 border-success-200 text-success-700 mb-4">
  <strong>Success!</strong> Your action was completed.
</div>
```

## 🛠 **Development Workflow**

### **1. Use Tailwind Classes**
Replace custom CSS classes with Tailwind utilities:

```html
<!-- Instead of custom CSS -->
<div class="my-custom-component">

<!-- Use Tailwind utilities -->
<div class="bg-white p-6 rounded-lg shadow-md">
```

### **2. Custom Components**
For complex components, use `@apply` in CSS:

```css
@layer components {
  .my-component {
    @apply bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-all;
  }
}
```

### **3. Responsive Design**
Use responsive prefixes:

```html
<div class="text-sm md:text-base lg:text-lg">
  Responsive text
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  Responsive grid
</div>
```

## 📱 **Responsive Breakpoints**

- `sm:` - 640px and up
- `md:` - 768px and up  
- `lg:` - 1024px and up
- `xl:` - 1280px and up
- `2xl:` - 1536px and up

## 🎯 **Best Practices**

1. **Use Utility Classes**: Prefer utilities over custom CSS
2. **Component Composition**: Build components by combining utilities
3. **Consistent Spacing**: Use the spacing scale (p-4, m-6, etc.)
4. **Semantic Colors**: Use color names that describe purpose (primary, success, error)
5. **Responsive First**: Always consider mobile-first design

## 🔧 **Configuration**

Your Tailwind config includes:
- ✅ Custom color palette (GoIndigo brand colors)
- ✅ Custom font families (Poppins, Bauhaus Pro)
- ✅ Extended spacing scale
- ✅ Custom shadows and border radius
- ✅ Forms and typography plugins

## 📚 **Resources**

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Tailwind UI Components](https://tailwindui.com/)
- [Headless UI](https://headlessui.com/) - Unstyled components
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) - VS Code extension

## 🎉 **You're Ready!**

Your design system is now powered by Tailwind CSS while maintaining your GoIndigo brand identity. Start using the new utility classes and enjoy the improved developer experience!
