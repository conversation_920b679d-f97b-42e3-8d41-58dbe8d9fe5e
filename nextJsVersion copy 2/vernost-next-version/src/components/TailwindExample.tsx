import React from 'react';

export default function TailwindExample() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-display font-medium text-primary-500 mb-8 text-center">
        Tailwind CSS Design System
      </h1>
      
      {/* Typography Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-neutral-800 mb-6">Typography</h2>
        <div className="space-y-4">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-display font-medium text-neutral-800">
            Display Heading (H1)
          </h1>
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-display font-medium text-neutral-800">
            Section Heading (H2)
          </h2>
          <h3 className="text-xl md:text-2xl lg:text-3xl font-semibold text-neutral-800">
            Subsection Heading (H3)
          </h3>
          <p className="text-base leading-relaxed text-neutral-700">
            This is body text using Poppins font. It demonstrates the responsive typography system 
            with proper line height and color contrast for optimal readability.
          </p>
        </div>
      </section>

      {/* Button Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-neutral-800 mb-6">Buttons</h2>
        <div className="flex flex-wrap gap-4">
          <button className="btn btn-primary">
            Primary Button
          </button>
          <button className="btn btn-secondary">
            Secondary Button
          </button>
          <button className="btn btn-outline">
            Outline Button
          </button>
          <button className="btn btn-ghost">
            Ghost Button
          </button>
        </div>
        
        <div className="flex flex-wrap gap-4 mt-4">
          <button className="btn btn-primary btn-sm">
            Small
          </button>
          <button className="btn btn-primary">
            Default
          </button>
          <button className="btn btn-primary btn-lg">
            Large
          </button>
        </div>
      </section>

      {/* Card Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-neutral-800 mb-6">Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Card Title</h3>
            </div>
            <div className="card-body">
              <p>This is a card component with hover effects and proper spacing.</p>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-base p-6 border border-neutral-100 transition-all hover:shadow-lg hover:-translate-y-0.5">
            <h3 className="text-xl font-semibold text-neutral-800 mb-2">Tailwind Card</h3>
            <p className="text-neutral-600">This card is built entirely with Tailwind utility classes.</p>
          </div>
          
          <div className="bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl p-6 text-white">
            <h3 className="text-xl font-semibold mb-2">Gradient Card</h3>
            <p className="text-primary-50">A beautiful gradient card using custom colors.</p>
          </div>
        </div>
      </section>

      {/* Form Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-neutral-800 mb-6">Forms</h2>
        <div className="max-w-md">
          <div className="form-group">
            <label htmlFor="email" className="block font-medium text-neutral-700 mb-2 text-sm">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none focus:bg-primary-50 transition-all"
              placeholder="Enter your email"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="message" className="block font-medium text-neutral-700 mb-2 text-sm">
              Message
            </label>
            <textarea
              id="message"
              rows={4}
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none focus:bg-primary-50 transition-all resize-vertical"
              placeholder="Your message here..."
            />
          </div>
          
          <button className="btn btn-primary w-full">
            Send Message
          </button>
        </div>
      </section>

      {/* Badge Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-neutral-800 mb-6">Badges</h2>
        <div className="flex flex-wrap gap-2">
          <span className="badge badge-primary">Primary</span>
          <span className="badge badge-success">Success</span>
          <span className="badge badge-error">Error</span>
          <span className="badge badge-warning">Warning</span>
          <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full uppercase tracking-wide bg-info-50 text-info-600">
            Info
          </span>
        </div>
      </section>

      {/* Alert Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-neutral-800 mb-6">Alerts</h2>
        <div className="space-y-4">
          <div className="alert alert-success">
            <strong>Success!</strong> Your changes have been saved successfully.
          </div>
          <div className="alert alert-error">
            <strong>Error!</strong> There was a problem processing your request.
          </div>
          <div className="alert alert-warning">
            <strong>Warning!</strong> Please review your information before proceeding.
          </div>
          <div className="alert alert-info">
            <strong>Info:</strong> This is some helpful information for you.
          </div>
        </div>
      </section>

      {/* Color Palette */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-neutral-800 mb-6">Color Palette</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="w-full h-20 bg-primary-500 rounded-lg mb-2"></div>
            <p className="text-sm font-medium">Primary</p>
          </div>
          <div className="text-center">
            <div className="w-full h-20 bg-secondary-500 rounded-lg mb-2"></div>
            <p className="text-sm font-medium">Secondary</p>
          </div>
          <div className="text-center">
            <div className="w-full h-20 bg-success-500 rounded-lg mb-2"></div>
            <p className="text-sm font-medium">Success</p>
          </div>
          <div className="text-center">
            <div className="w-full h-20 bg-error-500 rounded-lg mb-2"></div>
            <p className="text-sm font-medium">Error</p>
          </div>
        </div>
      </section>
    </div>
  );
}
