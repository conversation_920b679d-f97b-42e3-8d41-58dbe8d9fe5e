import type { Metada<PERSON> } from 'next';
import Script from 'next/script';
import { cookies } from 'next/headers';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { getDictionary } from '@/lib/dictionaries';
import { poppins, bauhaus, bauhausStdMedium } from '@/lib/fonts';
import GoogleAnalytics from '@/components/GoogleAnalytics';
import { GA_TRACKING_ID, getSEOConfig } from '@/lib/seo';
import '../styles/globals.css';

// Configuration for external scripts and URLs
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com';
const BASE_PATH = process.env.NODE_ENV === 'production' ? '/hotels' : '';
const FULL_SITE_URL = `${SITE_URL}${BASE_PATH}`;

const config = {
  url: {
    adobe: process.env.NEXT_PUBLIC_ADOBE_SCRIPT_URL || 'https://assets.adobedtm.com/your-adobe-script.js',
  },
};

// Generate metadata based on language
export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = await cookies();
  const langCookie = cookieStore.get('NEXT_LOCALE');
  const lang = langCookie?.value || 'en';

  const dictionary = await getDictionary(lang);
  const seoConfig = getSEOConfig(lang, dictionary);

  return {
    title: seoConfig.title,
    description: seoConfig.description,
    keywords: 'hotels, accommodation, booking, travel, vacation, resort',
    authors: [{ name: 'HotelBooker' }],
    creator: 'HotelBooker',
    publisher: 'HotelBooker',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: 'website',
      locale: seoConfig.openGraph?.locale || 'en_US',
      url: FULL_SITE_URL,
      siteName: 'HotelBooker',
      title: seoConfig.title,
      description: seoConfig.description,
      images: [
        {
          url: `${FULL_SITE_URL}/images/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: 'HotelBooker - Find Your Perfect Stay',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: '@hotelbooker',
      creator: '@hotelbooker',
      title: seoConfig.title,
      description: seoConfig.description,
      images: [`${FULL_SITE_URL}/images/og-image.jpg`],
    },
    alternates: {
      canonical: FULL_SITE_URL,
      languages: {
        'en-US': `${FULL_SITE_URL}/en`,
        'es-ES': `${FULL_SITE_URL}/es`,
        'ar-SA': `${FULL_SITE_URL}/ar`,
      },
    },
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
      yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
      yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
    },
    other: {
      'theme-color': '#0a5eb2',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'apple-mobile-web-app-title': 'HotelBooker',
      'application-name': 'HotelBooker',
      'msapplication-TileColor': '#0a5eb2',
    },
  };
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get the current language from cookies
  const cookieStore = await cookies();
  const langCookie = cookieStore.get('NEXT_LOCALE');
  const lang = langCookie?.value || 'en';

  // Get the dictionary for the current language
  const dictionary = await getDictionary(lang);

  // Determine if the language is RTL
  const isRTL = lang === 'ar';

  return (
    <html lang={lang} dir={isRTL ? 'rtl' : 'ltr'}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#0a5eb2" />
        <meta name="msapplication-TileColor" content="#0a5eb2" />
        <meta name="theme-color" content="#0a5eb2" />

        {/* Adobe Analytics Script */}
        <Script
          src={config.url.adobe}
          strategy="beforeInteractive"
          async
        />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://assets.adobedtm.com" />
      </head>
      <body className={`${poppins.variable} ${bauhaus.variable} ${bauhausStdMedium.variable}`}>
        <GoogleAnalytics gaId={GA_TRACKING_ID} />
        <LanguageProvider initialLang={lang as 'en' | 'es' | 'ar'} initialDictionary={dictionary}>
          {children}
        </LanguageProvider>
      </body>
    </html>
  );
}
