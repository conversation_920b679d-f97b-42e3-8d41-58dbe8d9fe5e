@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== CUSTOM CSS VARIABLES FOR LEGACY SUPPORT ===== */
:root {
  /* Legacy color mappings for backward compatibility */
  --background: #ffffff;
  --foreground: #262626;
  --primary-color: #0a5eb2;
  --secondary-color: #ff9e1b;
  --accent-color: #eff6ff;
  --light-gray: #fafafa;
  --medium-gray: #e5e5e5;
  --dark-gray: #737373;

  /* Font family variables for Next.js font optimization */
  --font-poppins: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-bauhaus: 'Bauhaus 93', Impact, 'Arial Black', sans-serif;
}

@layer base {
  html {
    @apply scroll-smooth;
    font-size: 16px;
    /* Base font size for rem calculations */
  }

  body {
    @apply max-w-full overflow-x-hidden font-sans text-base font-normal leading-normal text-neutral-800 bg-white antialiased min-h-screen;
    text-rendering: optimizeLegibility;
    letter-spacing: -0.025em;
  }

  /* Typography base styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply leading-tight text-neutral-800 font-semibold;
  }

  h1,
  h2 {
    @apply font-display font-medium;
    letter-spacing: -0.05em;
  }

  h3,
  h4,
  h5,
  h6 {
    @apply font-sans font-semibold;
    letter-spacing: -0.025em;
  }

  h1 {
    @apply text-3xl mb-6 md:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl mb-5 md:text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-xl mb-4 md:text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-lg mb-3;
  }

  h5 {
    @apply text-md mb-3;
  }

  h6 {
    @apply text-base mb-2;
  }

  p {
    @apply font-sans text-base leading-relaxed mb-4 text-neutral-700;
  }

  a {
    @apply text-primary-500 no-underline transition-colors hover:text-primary-600 hover:underline;
  }

  a:focus {
    @apply outline-2 outline-primary-500 outline-offset-2 rounded-sm;
  }

  /* Form elements base styles */
  input,
  select,
  textarea {
    @apply font-sans text-base font-normal leading-normal text-neutral-800 bg-white border border-neutral-300 rounded-lg px-4 py-3 w-full transition-all;
  }

  input:focus,
  select:focus,
  textarea:focus {
    @apply border-primary-500 outline-none bg-primary-50;
    box-shadow: 0 0 0 3px rgba(10, 94, 178, 0.1);
  }

  input:hover:not(:focus),
  select:hover:not(:focus),
  textarea:hover:not(:focus) {
    @apply border-neutral-400;
  }

  input::placeholder,
  textarea::placeholder {
    @apply text-neutral-500 font-normal;
  }

  input:disabled,
  select:disabled,
  textarea:disabled {
    @apply bg-neutral-100 border-neutral-200 text-neutral-500 cursor-not-allowed;
  }

  button {
    @apply font-sans font-medium text-base cursor-pointer border-none rounded-lg px-6 py-3 transition-all inline-flex items-center justify-center gap-2 text-center leading-none relative overflow-hidden;
  }

  button:disabled {
    @apply opacity-60 cursor-not-allowed;
  }

  label {
    @apply block font-medium text-neutral-700 mb-2 text-sm;
  }

  /* Focus styles for accessibility */
  *:focus {
    @apply outline-2 outline-primary-500 outline-offset-2 rounded-sm;
  }

  *:focus:not(:focus-visible) {
    @apply outline-none;
  }

  /* Image optimization */
  img {
    @apply max-w-full h-auto;
  }
}

@layer components {

  /* ===== BUTTON COMPONENTS ===== */
  .btn {
    @apply font-sans font-medium text-base cursor-pointer border-none rounded-lg px-6 py-3 transition-all inline-flex items-center justify-center gap-2 text-center leading-none relative overflow-hidden;
  }

  .btn:disabled {
    @apply opacity-60 cursor-not-allowed transform-none;
  }

  .btn-primary {
    @apply bg-primary-500 text-white font-display font-medium uppercase tracking-wide shadow-sm;
  }

  .btn-primary:hover:not(:disabled) {
    @apply bg-primary-600 -translate-y-0.5 shadow-md;
  }

  .btn-primary:active {
    @apply translate-y-0 shadow-sm;
  }

  .btn-secondary {
    @apply bg-secondary-500 text-white font-semibold shadow-sm;
  }

  .btn-secondary:hover:not(:disabled) {
    @apply bg-secondary-600 -translate-y-0.5 shadow-md;
  }

  .btn-outline {
    @apply bg-transparent text-primary-500 border-2 border-primary-500;
  }

  .btn-outline:hover:not(:disabled) {
    @apply bg-primary-500 text-white -translate-y-0.5 shadow-md;
  }

  .btn-ghost {
    @apply bg-transparent text-primary-500 border-none shadow-none;
  }

  .btn-ghost:hover:not(:disabled) {
    @apply bg-primary-50 text-primary-600;
  }

  /* Button Sizes */
  .btn-sm {
    @apply px-4 py-2 text-sm rounded-md;
  }

  .btn-lg {
    @apply px-8 py-4 text-lg rounded-xl;
  }

  .btn-xl {
    @apply px-10 py-5 text-xl rounded-2xl;
  }

  /* Button States */
  .btn-success {
    @apply bg-success-500 text-white;
  }

  .btn-success:hover:not(:disabled) {
    @apply bg-success-600;
  }

  .btn-error {
    @apply bg-error-500 text-white;
  }

  .btn-error:hover:not(:disabled) {
    @apply bg-error-600;
  }

  .btn-warning {
    @apply bg-warning-500 text-white;
  }

  .btn-warning:hover:not(:disabled) {
    @apply bg-warning-600;
  }

  /* ===== FORM COMPONENTS ===== */
  .form-group {
    @apply mb-6;
  }

  .form-group label {
    @apply mb-2;
  }

  .form-error {
    @apply text-error-500 text-sm mt-1 block;
  }

  .form-success {
    @apply text-success-500 text-sm mt-1 block;
  }

  .form-help {
    @apply text-neutral-500 text-sm mt-1 block;
  }

  .input-sm {
    @apply px-3 py-2 text-sm rounded-md;
  }

  .input-lg {
    @apply px-5 py-4 text-lg rounded-xl;
  }

  /* Input States */
  .input-error {
    @apply border-error-500;
  }

  .input-error:focus {
    @apply bg-error-50;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  /* ===== CARD COMPONENTS ===== */
  .card {
    @apply bg-white rounded-xl shadow-base p-6 border border-neutral-100 transition-all;
  }

  .card:hover {
    @apply shadow-lg -translate-y-0.5;
  }

  .card-header {
    @apply mb-4 pb-4 border-b border-neutral-100;
  }

  .card-title {
    @apply text-xl font-semibold text-neutral-800 mb-2;
  }

  .card-body {
    @apply text-neutral-600;
  }

  /* ===== BADGE COMPONENTS ===== */
  .badge {
    @apply inline-flex items-center px-3 py-1 text-xs font-medium rounded-full uppercase tracking-wide;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-700;
  }

  .badge-success {
    @apply bg-success-50 text-success-600;
  }

  .badge-error {
    @apply bg-error-50 text-error-600;
  }

  .badge-warning {
    @apply bg-warning-50 text-warning-600;
  }

  /* ===== ALERT COMPONENTS ===== */
  .alert {
    @apply p-4 rounded-lg border mb-4;
  }

  .alert-success {
    @apply bg-success-50 border-success-200 text-success-700;
  }

  .alert-error {
    @apply bg-error-50 border-error-200 text-error-700;
  }

  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-700;
  }

  .alert-info {
    @apply bg-info-50 border-info-200 text-info-700;
  }

  /* ===== CONTAINER COMPONENTS ===== */
  .container {
    @apply w-full max-w-screen-xl mx-auto px-4;
  }

  .container-sm {
    @apply max-w-screen-sm;
  }

  .container-md {
    @apply max-w-screen-md;
  }

  .container-lg {
    @apply max-w-screen-lg;
  }

  .container-xl {
    @apply max-w-screen-xl;
  }
}

@layer utilities {

  /* ===== ACCESSIBILITY UTILITIES ===== */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }

  .skip-link {
    @apply absolute -top-10 left-1.5 bg-primary-500 text-white px-4 py-2 no-underline rounded-md z-50 transition-all;
  }

  .skip-link:focus {
    @apply top-1.5;
  }

  /* ===== RESPONSIVE UTILITIES ===== */
  .container-responsive {
    @apply w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-12;
  }

  /* ===== ANIMATION UTILITIES ===== */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* ===== DARK MODE UTILITIES ===== */
  .dark-mode {
    @apply bg-neutral-900 text-neutral-100;
  }

  .light-mode {
    @apply bg-white text-neutral-800;
  }
}

/* ===== MEDIA QUERIES FOR ACCESSIBILITY ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0066cc;
    --secondary-color: #ff6600;
    --foreground: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #171717;
    --foreground: #f5f5f5;
  }
}

/* Print styles */
@media print {

  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  .no-print {
    display: none !important;
  }
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  direction: rtl;
}

/* ===== CUSTOM FORM ENHANCEMENTS ===== */

/* Select dropdown arrow */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 2.5rem;
  appearance: none;
}

/* Textarea resize */
textarea {
  resize: vertical;
  min-height: 120px;
}

/* Checkbox and Radio accent color */
input[type="checkbox"],
input[type="radio"] {
  accent-color: theme('colors.primary.500');
}

/* ===== END OF TAILWIND CONVERSION ===== */



/* ===== LEGACY COMPONENT STYLES (will be converted to Tailwind classes) ===== */

/* These styles are kept for backward compatibility */
/* Gradually replace with Tailwind utility classes in your components */

.legacy-card {
  @apply bg-white rounded-xl shadow-base p-6 border border-neutral-100 transition-all hover:shadow-lg hover:-translate-y-0.5;
}

.legacy-badge {
  @apply inline-flex items-center px-3 py-1 text-xs font-medium rounded-full uppercase tracking-wide;
}

.legacy-alert {
  @apply p-4 rounded-lg border mb-4;
}