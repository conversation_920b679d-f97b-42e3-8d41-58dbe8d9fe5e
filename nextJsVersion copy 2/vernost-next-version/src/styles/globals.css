@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== CUSTOM CSS VARIABLES FOR LEGACY SUPPORT ===== */
:root {
  /* Legacy color mappings for backward compatibility */
  --background: #ffffff;
  --foreground: #262626;
  --primary-color: #0a5eb2;
  --secondary-color: #ff9e1b;
  --accent-color: #eff6ff;
  --light-gray: #fafafa;
  --medium-gray: #e5e5e5;
  --dark-gray: #737373;

  /* Font family variables for Next.js font optimization */
  --font-poppins: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-bauhaus: 'Bauhaus 93', Impact, 'Arial Black', sans-serif;
}

@layer base {
  html {
    @apply scroll-smooth;
    font-size: 16px;
    /* Base font size for rem calculations */
  }

  body {
    @apply max-w-full overflow-x-hidden font-sans text-base font-normal leading-normal text-neutral-800 bg-white antialiased min-h-screen;
    text-rendering: optimizeLegibility;
    letter-spacing: -0.025em;
  }

  /* Typography base styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply leading-tight text-neutral-800 font-semibold;
  }

  h1,
  h2 {
    @apply font-display font-medium;
    letter-spacing: -0.05em;
  }

  h3,
  h4,
  h5,
  h6 {
    @apply font-sans font-semibold;
    letter-spacing: -0.025em;
  }

  h1 {
    @apply text-3xl mb-6 md:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl mb-5 md:text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-xl mb-4 md:text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-lg mb-3;
  }

  h5 {
    @apply text-md mb-3;
  }

  h6 {
    @apply text-base mb-2;
  }

  p {
    @apply font-sans text-base leading-relaxed mb-4 text-neutral-700;
  }

  a {
    @apply text-primary-500 no-underline transition-colors hover:text-primary-600 hover:underline;
  }

  a:focus {
    @apply outline-2 outline-primary-500 outline-offset-2 rounded-sm;
  }

  /* Form elements base styles */
  input,
  select,
  textarea {
    @apply font-sans text-base font-normal leading-normal text-neutral-800 bg-white border border-neutral-300 rounded-lg px-4 py-3 w-full transition-all;
  }

  input:focus,
  select:focus,
  textarea:focus {
    @apply border-primary-500 outline-none bg-primary-50;
    box-shadow: 0 0 0 3px rgba(10, 94, 178, 0.1);
  }

  input:hover:not(:focus),
  select:hover:not(:focus),
  textarea:hover:not(:focus) {
    @apply border-neutral-400;
  }

  input::placeholder,
  textarea::placeholder {
    @apply text-neutral-500 font-normal;
  }

  input:disabled,
  select:disabled,
  textarea:disabled {
    @apply bg-neutral-100 border-neutral-200 text-neutral-500 cursor-not-allowed;
  }

  button {
    @apply font-sans font-medium text-base cursor-pointer border-none rounded-lg px-6 py-3 transition-all inline-flex items-center justify-center gap-2 text-center leading-none relative overflow-hidden;
  }

  button:disabled {
    @apply opacity-60 cursor-not-allowed;
  }

  label {
    @apply block font-medium text-neutral-700 mb-2 text-sm;
  }

  /* Focus styles for accessibility */
  *:focus {
    @apply outline-2 outline-primary-500 outline-offset-2 rounded-sm;
  }

  *:focus:not(:focus-visible) {
    @apply outline-none;
  }

  /* Image optimization */
  img {
    @apply max-w-full h-auto;
  }
}

/* ===== COMPONENT CLASSES (Optional - can be replaced with utility classes) ===== */

/* If you prefer to keep some component classes, uncomment the ones you want to use */

/*
@layer components {
  .btn {
    @apply font-sans font-medium text-base cursor-pointer border-none rounded-lg px-6 py-3 transition-all inline-flex items-center justify-center gap-2 text-center leading-none relative overflow-hidden;
  }

  .btn-primary {
    @apply bg-primary-500 text-white font-display font-medium uppercase tracking-wide shadow-sm hover:bg-primary-600 hover:-translate-y-0.5 hover:shadow-md;
  }

  .card {
    @apply bg-white rounded-xl shadow-base p-6 border border-neutral-100 transition-all hover:shadow-lg hover:-translate-y-0.5;
  }
}
*/

@layer utilities {

  /* ===== ACCESSIBILITY UTILITIES ===== */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .skip-link {
    position: absolute;
    top: -2.5rem;
    left: 0.375rem;
    background-color: theme('colors.primary.500');
    color: white;
    padding: 0.5rem 1rem;
    text-decoration: none;
    border-radius: 0.375rem;
    z-index: 50;
    transition: all 150ms ease;
  }

  .skip-link:focus {
    top: 0.375rem;
  }

  /* ===== ANIMATION UTILITIES ===== */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

/* ===== MEDIA QUERIES FOR ACCESSIBILITY ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0066cc;
    --secondary-color: #ff6600;
    --foreground: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #171717;
    --foreground: #f5f5f5;
  }
}

/* Print styles */
@media print {

  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  .no-print {
    display: none !important;
  }
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  direction: rtl;
}

/* ===== CUSTOM FORM ENHANCEMENTS ===== */

/* Select dropdown arrow */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 2.5rem;
  appearance: none;
}

/* Textarea resize */
textarea {
  resize: vertical;
  min-height: 120px;
}

/* Checkbox and Radio accent color */
input[type="checkbox"],
input[type="radio"] {
  accent-color: theme('colors.primary.500');
}

/* ===== END OF TAILWIND CONVERSION ===== */



/* ===== LEGACY COMPONENT STYLES (will be converted to Tailwind classes) ===== */

/* These styles are kept for backward compatibility */
/* Gradually replace with Tailwind utility classes in your components */

.legacy-card {
  @apply bg-white rounded-xl shadow-base p-6 border border-neutral-100 transition-all hover:shadow-lg hover:-translate-y-0.5;
}

.legacy-badge {
  @apply inline-flex items-center px-3 py-1 text-xs font-medium rounded-full uppercase tracking-wide;
}

.legacy-alert {
  @apply p-4 rounded-lg border mb-4;
}