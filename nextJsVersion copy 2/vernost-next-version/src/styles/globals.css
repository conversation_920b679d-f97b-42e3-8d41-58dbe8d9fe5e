:root {
  /* Colors similar to GoIndigo */
  --background: #ffffff;
  --foreground: #333333;
  --primary-color: #0a5eb2;
  /* GoIndigo blue */
  --secondary-color: #ff9e1b;
  /* GoIndigo orange */
  --accent-color: #e6eef8;
  /* Light blue background */
  --light-gray: #f8f9fa;
  --medium-gray: #e1e5e9;
  --dark-gray: #6c757d;

  /* Font families similar to GoIndigo */
  --font-family-poppins-semibold: var(--font-poppins), 'Poppins-SemiBold', sans-serif;
  --font-family-poppins-medium: var(--font-poppins), 'Poppins-Medium', sans-serif;
  --font-family-poppins-regular: var(--font-poppins), 'Poppins-Regular', sans-serif;
  --font-family-poppins-light: var(--font-poppins), 'Poppins-Light', sans-serif;
  --font-family-bauhaus-std-medium: var(--font-bauhaus-std-medium), 'BauhausStd-Medium', sans-serif;
  --font-family-bauhaus-pro: var(--font-bauhaus), 'Bauhaus Pro', sans-serif;

  /* Default font settings */
  --font-sans: var(--font-family-poppins-regular);
  --font-display: var(--font-family-bauhaus-pro);
  --font-size-xs: 0.75rem;
  /* 12px */
  --font-size-sm: 0.875rem;
  /* 14px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-md: 1.125rem;
  /* 18px */
  --font-size-lg: 1.25rem;
  /* 20px */
  --font-size-xl: 1.5rem;
  /* 24px */
  --font-size-2xl: 1.875rem;
  /* 30px */
  --font-size-3xl: 2.25rem;
  /* 36px */

  /* Font weights similar to GoIndigo */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Line heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Border radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-regular);
  line-height: var(--line-height-normal);
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.01em;
  /* Slightly tighter letter spacing like GoIndigo */
}

/* Make sure the font is applied to all text with GoIndigo-like styling */
h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: var(--line-height-tight);
  color: var(--foreground);
  margin-bottom: 0.5em;
}

/* Use Bauhaus Pro for main headings like GoIndigo */
h1,
h2 {
  font-family: var(--font-display);
  font-weight: var(--font-medium);
  letter-spacing: -0.02em;
}

/* Use Poppins for smaller headings */
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-poppins-medium);
  font-weight: var(--font-medium);
}

h1 {
  font-size: var(--font-size-3xl);
}

h2 {
  font-size: var(--font-size-2xl);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5 {
  font-size: var(--font-size-md);
}

h6 {
  font-size: var(--font-size-base);
}

p {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: 1em;
}

span,
a,
button,
input,
select,
textarea {
  font-family: var(--font-sans);
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font-family: var(--font-sans);
  font-weight: var(--font-medium);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* GoIndigo-style primary button with Bauhaus Pro font */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.75rem 1.5rem;
  font-family: var(--font-display);
  font-weight: var(--font-medium);
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

.btn-primary:hover {
  background-color: #084a8f;
  /* Darker blue on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* GoIndigo-style secondary button */
.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.75rem 1.5rem;
  font-family: var(--font-family-poppins-semibold);
  font-weight: var(--font-semibold);
}

.btn-secondary:hover {
  background-color: #e68a00;
  /* Darker orange on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

input,
select,
textarea {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  padding: 0.75rem 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--border-radius-md);
  transition: border-color 0.2s ease;
}

input:focus,
select:focus,
textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(10, 94, 178, 0.2);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  direction: rtl;
}

/* RTL specific adjustments */
[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
  text-align: right;
}

[dir="rtl"] button {
  text-align: center;
}