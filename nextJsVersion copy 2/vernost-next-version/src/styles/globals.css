@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== CUSTOM CSS VARIABLES FOR LEGACY SUPPORT ===== */
:root {
  /* Legacy color mappings for backward compatibility */
  --background: #ffffff;
  --foreground: #262626;
  --primary-color: #0a5eb2;
  --secondary-color: #ff9e1b;
  --accent-color: #eff6ff;
  --light-gray: #fafafa;
  --medium-gray: #e5e5e5;
  --dark-gray: #737373;

  /* Font family variables for Next.js font optimization */
  --font-poppins: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-bauhaus: 'Bauhaus 93', Impact, 'Arial Black', sans-serif;
}

@layer base {
  html {
    @apply scroll-smooth;
    font-size: 16px;
    /* Base font size for rem calculations */
  }

  body {
    @apply max-w-full overflow-x-hidden font-sans text-base font-normal leading-normal text-neutral-800 bg-white antialiased min-h-screen;
    text-rendering: optimizeLegibility;
    letter-spacing: -0.025em;
  }

  /* Typography base styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply leading-tight text-neutral-800 font-semibold;
  }

  h1,
  h2 {
    @apply font-display font-medium;
    letter-spacing: -0.05em;
  }

  h3,
  h4,
  h5,
  h6 {
    @apply font-sans font-semibold;
    letter-spacing: -0.025em;
  }

  h1 {
    @apply text-3xl mb-6 md:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl mb-5 md:text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-xl mb-4 md:text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-lg mb-3;
  }

  h5 {
    @apply text-md mb-3;
  }

  h6 {
    @apply text-base mb-2;
  }

  p {
    @apply font-sans text-base leading-relaxed mb-4 text-neutral-700;
  }

  a {
    @apply text-primary-500 no-underline transition-colors hover:text-primary-600 hover:underline;
  }

  a:focus {
    @apply outline-2 outline-primary-500 outline-offset-2 rounded-sm;
  }

  /* Form elements base styles */
  input,
  select,
  textarea {
    @apply font-sans text-base font-normal leading-normal text-neutral-800 bg-white border border-neutral-300 rounded-lg px-4 py-3 w-full transition-all;
  }

  input:focus,
  select:focus,
  textarea:focus {
    @apply border-primary-500 outline-none bg-primary-50;
    box-shadow: 0 0 0 3px rgba(10, 94, 178, 0.1);
  }

  input:hover:not(:focus),
  select:hover:not(:focus),
  textarea:hover:not(:focus) {
    @apply border-neutral-400;
  }

  input::placeholder,
  textarea::placeholder {
    @apply text-neutral-500 font-normal;
  }

  input:disabled,
  select:disabled,
  textarea:disabled {
    @apply bg-neutral-100 border-neutral-200 text-neutral-500 cursor-not-allowed;
  }

  button {
    @apply font-sans font-medium text-base cursor-pointer border-none rounded-lg px-6 py-3 transition-all inline-flex items-center justify-center gap-2 text-center leading-none relative overflow-hidden;
  }

  button:disabled {
    @apply opacity-60 cursor-not-allowed;
  }

  label {
    @apply block font-medium text-neutral-700 mb-2 text-sm;
  }

  /* Focus styles for accessibility */
  *:focus {
    @apply outline-2 outline-primary-500 outline-offset-2 rounded-sm;
  }

  *:focus:not(:focus-visible) {
    @apply outline-none;
  }

  /* Image optimization */
  img {
    @apply max-w-full h-auto;
  }
}

@layer components {

  /* ===== BUTTON COMPONENTS ===== */
  .btn {
    @apply font-sans font-medium text-base cursor-pointer border-none rounded-lg px-6 py-3 transition-all inline-flex items-center justify-center gap-2 text-center leading-none relative overflow-hidden;
  }

  .btn:disabled {
    @apply opacity-60 cursor-not-allowed transform-none;
  }

  .btn-primary {
    @apply bg-primary-500 text-white font-display font-medium uppercase tracking-wide shadow-sm;
  }

  .btn-primary:hover:not(:disabled) {
    @apply bg-primary-600 -translate-y-0.5 shadow-md;
  }

  .btn-primary:active {
    @apply translate-y-0 shadow-sm;
  }

  .btn-secondary {
    @apply bg-secondary-500 text-white font-semibold shadow-sm;
  }

  .btn-secondary:hover:not(:disabled) {
    @apply bg-secondary-600 -translate-y-0.5 shadow-md;
  }

  .btn-outline {
    @apply bg-transparent text-primary-500 border-2 border-primary-500;
  }

  .btn-outline:hover:not(:disabled) {
    @apply bg-primary-500 text-white -translate-y-0.5 shadow-md;
  }

  .btn-ghost {
    @apply bg-transparent text-primary-500 border-none shadow-none;
  }

  .btn-ghost:hover:not(:disabled) {
    @apply bg-primary-50 text-primary-600;
  }

  /* Button Sizes */
  .btn-sm {
    @apply px-4 py-2 text-sm rounded-md;
  }

  .btn-lg {
    @apply px-8 py-4 text-lg rounded-xl;
  }

  .btn-xl {
    @apply px-10 py-5 text-xl rounded-2xl;
  }

  /* Button States */
  .btn-success {
    @apply bg-success-500 text-white;
  }

  .btn-success:hover:not(:disabled) {
    @apply bg-success-600;
  }

  .btn-error {
    @apply bg-error-500 text-white;
  }

  .btn-error:hover:not(:disabled) {
    @apply bg-error-600;
  }

  .btn-warning {
    @apply bg-warning-500 text-white;
  }

  .btn-warning:hover:not(:disabled) {
    @apply bg-warning-600;
  }

  /* ===== FORM COMPONENTS ===== */
  .form-group {
    @apply mb-6;
  }

  .form-group label {
    @apply mb-2;
  }

  .form-error {
    @apply text-error-500 text-sm mt-1 block;
  }

  .form-success {
    @apply text-success-500 text-sm mt-1 block;
  }

  .form-help {
    @apply text-neutral-500 text-sm mt-1 block;
  }

  .input-sm {
    @apply px-3 py-2 text-sm rounded-md;
  }

  .input-lg {
    @apply px-5 py-4 text-lg rounded-xl;
  }

  /* Input States */
  .input-error {
    @apply border-error-500;
  }

  .input-error:focus {
    @apply bg-error-50;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  /* ===== CARD COMPONENTS ===== */
  .card {
    @apply bg-white rounded-xl shadow-base p-6 border border-neutral-100 transition-all;
  }

  .card:hover {
    @apply shadow-lg -translate-y-0.5;
  }

  .card-header {
    @apply mb-4 pb-4 border-b border-neutral-100;
  }

  .card-title {
    @apply text-xl font-semibold text-neutral-800 mb-2;
  }

  .card-body {
    @apply text-neutral-600;
  }

  /* ===== BADGE COMPONENTS ===== */
  .badge {
    @apply inline-flex items-center px-3 py-1 text-xs font-medium rounded-full uppercase tracking-wide;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-700;
  }

  .badge-success {
    @apply bg-success-50 text-success-600;
  }

  .badge-error {
    @apply bg-error-50 text-error-600;
  }

  .badge-warning {
    @apply bg-warning-50 text-warning-600;
  }

  /* ===== ALERT COMPONENTS ===== */
  .alert {
    @apply p-4 rounded-lg border mb-4;
  }

  .alert-success {
    @apply bg-success-50 border-success-200 text-success-700;
  }

  .alert-error {
    @apply bg-error-50 border-error-200 text-error-700;
  }

  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-700;
  }

  .alert-info {
    @apply bg-info-50 border-info-200 text-info-700;
  }

  /* ===== CONTAINER COMPONENTS ===== */
  .container {
    @apply w-full max-w-screen-xl mx-auto px-4;
  }

  .container-sm {
    @apply max-w-screen-sm;
  }

  .container-md {
    @apply max-w-screen-md;
  }

  .container-lg {
    @apply max-w-screen-lg;
  }

  .container-xl {
    @apply max-w-screen-xl;
  }
}

@layer utilities {

  /* ===== ACCESSIBILITY UTILITIES ===== */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }

  .skip-link {
    @apply absolute -top-10 left-1.5 bg-primary-500 text-white px-4 py-2 no-underline rounded-md z-50 transition-all;
  }

  .skip-link:focus {
    @apply top-1.5;
  }

  /* ===== RESPONSIVE UTILITIES ===== */
  .container-responsive {
    @apply w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-12;
  }

  /* ===== ANIMATION UTILITIES ===== */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* ===== DARK MODE UTILITIES ===== */
  .dark-mode {
    @apply bg-neutral-900 text-neutral-100;
  }

  .light-mode {
    @apply bg-white text-neutral-800;
  }
}

/* ===== MEDIA QUERIES FOR ACCESSIBILITY ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0066cc;
    --secondary-color: #ff6600;
    --foreground: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #171717;
    --foreground: #f5f5f5;
  }
}

/* Print styles */
@media print {

  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  .no-print {
    display: none !important;
  }
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  direction: rtl;
}

/* ===== FORM CONTROLS ===== */
input,
select,
textarea {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-regular);
  line-height: var(--line-height-normal);
  color: var(--neutral-800);
  background-color: var(--background);
  border: var(--border-width-1) solid var(--neutral-300);
  border-radius: var(--border-radius-lg);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-all);
  width: 100%;
}

input:focus,
select:focus,
textarea:focus {
  border-color: var(--primary-500);
  outline: none;
  box-shadow: 0 0 0 3px rgba(10, 94, 178, 0.1);
  background-color: var(--primary-50);
}

input:hover:not(:focus),
select:hover:not(:focus),
textarea:hover:not(:focus) {
  border-color: var(--neutral-400);
}

input::placeholder,
textarea::placeholder {
  color: var(--neutral-500);
  font-weight: var(--font-regular);
}

/* Input States */
input:invalid,
select:invalid,
textarea:invalid {
  border-color: var(--error-500);
}

input:invalid:focus,
select:invalid:focus,
textarea:invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  background-color: var(--error-50);
}

input:disabled,
select:disabled,
textarea:disabled {
  background-color: var(--neutral-100);
  border-color: var(--neutral-200);
  color: var(--neutral-500);
  cursor: not-allowed;
}

/* Input Sizes */
.input-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-md);
}

.input-lg {
  padding: var(--space-4) var(--space-5);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-xl);
}

/* Textarea specific */
textarea {
  resize: vertical;
  min-height: 120px;
}

/* Select specific */
select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--space-10);
  appearance: none;
}

/* Checkbox and Radio */
input[type="checkbox"],
input[type="radio"] {
  width: auto;
  margin-right: var(--space-2);
  accent-color: var(--primary-500);
}

/* Labels */
label {
  display: block;
  font-weight: var(--font-medium);
  color: var(--neutral-700);
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
}

/* Form Groups */
.form-group {
  margin-bottom: var(--space-6);
}

.form-group label {
  margin-bottom: var(--space-2);
}

/* Error Messages */
.form-error {
  color: var(--error-500);
  font-size: var(--font-size-sm);
  margin-top: var(--space-1);
  display: block;
}

/* Success Messages */
.form-success {
  color: var(--success-500);
  font-size: var(--font-size-sm);
  margin-top: var(--space-1);
  display: block;
}

/* Help Text */
.form-help {
  color: var(--neutral-500);
  font-size: var(--font-size-sm);
  margin-top: var(--space-1);
  display: block;
}

/* ===== UTILITY CLASSES ===== */

/* Layout Utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

/* Grid Utilities */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.gap-1 {
  gap: var(--space-1);
}

.gap-2 {
  gap: var(--space-2);
}

.gap-3 {
  gap: var(--space-3);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.gap-8 {
  gap: var(--space-8);
}

/* Spacing Utilities */
.m-0 {
  margin: 0;
}

.m-1 {
  margin: var(--space-1);
}

.m-2 {
  margin: var(--space-2);
}

.m-3 {
  margin: var(--space-3);
}

.m-4 {
  margin: var(--space-4);
}

.m-6 {
  margin: var(--space-6);
}

.m-8 {
  margin: var(--space-8);
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: var(--space-1);
}

.mt-2 {
  margin-top: var(--space-2);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-6 {
  margin-top: var(--space-6);
}

.mt-8 {
  margin-top: var(--space-8);
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: var(--space-1);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: var(--space-1);
}

.p-2 {
  padding: var(--space-2);
}

.p-3 {
  padding: var(--space-3);
}

.p-4 {
  padding: var(--space-4);
}

.p-6 {
  padding: var(--space-6);
}

.p-8 {
  padding: var(--space-8);
}

/* Background Colors */
.bg-primary {
  background-color: var(--primary-500);
}

.bg-primary-light {
  background-color: var(--primary-50);
}

.bg-secondary {
  background-color: var(--secondary-500);
}

.bg-white {
  background-color: white;
}

.bg-gray-50 {
  background-color: var(--neutral-50);
}

.bg-gray-100 {
  background-color: var(--neutral-100);
}

.bg-gray-200 {
  background-color: var(--neutral-200);
}

/* Border Utilities */
.border {
  border: var(--border-width-1) solid var(--neutral-200);
}

.border-t {
  border-top: var(--border-width-1) solid var(--neutral-200);
}

.border-b {
  border-bottom: var(--border-width-1) solid var(--neutral-200);
}

.border-primary {
  border-color: var(--primary-500);
}

.rounded {
  border-radius: var(--border-radius-base);
}

.rounded-md {
  border-radius: var(--border-radius-md);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.rounded-xl {
  border-radius: var(--border-radius-xl);
}

.rounded-full {
  border-radius: var(--border-radius-full);
}

/* Shadow Utilities */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow {
  box-shadow: var(--shadow-base);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* Position Utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* Display Utilities */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.hidden {
  display: none;
}

/* Width & Height */
.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.h-full {
  height: 100%;
}

.h-auto {
  height: auto;
}

.min-h-screen {
  min-height: 100vh;
}

/* Text Alignment */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* Overflow */
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* Transitions */
.transition {
  transition: var(--transition-all);
}

.transition-colors {
  transition: var(--transition-colors);
}

.transition-transform {
  transition: var(--transition-transform);
}

/* Transform */
.transform {
  transform: translateZ(0);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.hover\:-translate-y-1:hover {
  transform: translateY(-0.25rem);
}

/* ===== COMPONENT STYLES ===== */

/* Card Component */
.card {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  padding: var(--space-6);
  border: var(--border-width-1) solid var(--neutral-100);
  transition: var(--transition-all);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: var(--border-width-1) solid var(--neutral-100);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
}

.card-body {
  color: var(--neutral-600);
}

/* Badge Component */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

.badge-success {
  background-color: var(--success-50);
  color: var(--success-600);
}

.badge-error {
  background-color: var(--error-50);
  color: var(--error-600);
}

.badge-warning {
  background-color: var(--warning-50);
  color: var(--warning-600);
}

/* Alert Component */
.alert {
  padding: var(--space-4);
  border-radius: var(--border-radius-lg);
  border: var(--border-width-1) solid;
  margin-bottom: var(--space-4);
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-700);
}

.alert-error {
  background-color: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-700);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-700);
}

.alert-info {
  background-color: var(--info-50);
  border-color: var(--info-200);
  color: var(--info-700);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile First Responsive Utilities */
@media (min-width: 640px) {
  .sm\:text-lg {
    font-size: var(--font-size-lg);
  }

  .sm\:text-xl {
    font-size: var(--font-size-xl);
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:p-6 {
    padding: var(--space-6);
  }

  .sm\:p-8 {
    padding: var(--space-8);
  }
}

@media (min-width: 768px) {
  .md\:text-xl {
    font-size: var(--font-size-xl);
  }

  .md\:text-2xl {
    font-size: var(--font-size-2xl);
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:p-8 {
    padding: var(--space-8);
  }

  .md\:p-12 {
    padding: var(--space-12);
  }
}

@media (min-width: 1024px) {
  .lg\:text-2xl {
    font-size: var(--font-size-2xl);
  }

  .lg\:text-3xl {
    font-size: var(--font-size-3xl);
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:p-12 {
    padding: var(--space-12);
  }

  .lg\:p-16 {
    padding: var(--space-16);
  }
}

/* ===== ACCESSIBILITY ===== */

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* Remove focus outline for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-500: #0066cc;
    --secondary-500: #ff6600;
    --neutral-800: #000000;
    --neutral-600: #333333;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-500);
  color: white;
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  z-index: var(--z-toast);
  transition: var(--transition-all);
}

.skip-link:focus {
  top: 6px;
}

/* ===== RTL SUPPORT ===== */

[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  direction: rtl;
}

/* RTL specific adjustments */
[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
  text-align: right;
}

[dir="rtl"] button {
  text-align: center;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* ===== DARK MODE SUPPORT ===== */

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--neutral-900);
    --foreground: var(--neutral-100);
    --primary-50: #1e3a8a;
    --primary-100: #1e40af;
    --neutral-50: var(--neutral-800);
    --neutral-100: var(--neutral-700);
    --neutral-200: var(--neutral-600);
  }

  .card {
    background: var(--neutral-800);
    border-color: var(--neutral-700);
  }

  input,
  select,
  textarea {
    background-color: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-100);
  }

  input:focus,
  select:focus,
  textarea:focus {
    background-color: var(--neutral-700);
  }
}

/* Force light mode class */
.light-mode {
  --background: #ffffff;
  --foreground: var(--neutral-800);
}

/* Force dark mode class */
.dark-mode {
  --background: var(--neutral-900);
  --foreground: var(--neutral-100);
}

/* ===== PRINT STYLES ===== */

@media print {

  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  abbr[title]::after {
    content: " (" attr(title) ")";
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  .no-print {
    display: none !important;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* GPU acceleration for animations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize font loading - this is a general rule for all @font-face declarations */
/* Individual font files should include: font-display: swap; */

/* Optimize images */
img {
  max-width: 100%;
  height: auto;
}

/* Lazy loading support */
img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
  opacity: 1;
}