:root {
  /* ===== COLOR SYSTEM ===== */
  /* Primary Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #0a5eb2;
  /* Main brand blue */
  --primary-600: #084a8f;
  --primary-700: #073a6b;
  --primary-800: #062a47;
  --primary-900: #051a23;

  /* Secondary Brand Colors */
  --secondary-50: #fff7ed;
  --secondary-100: #ffedd5;
  --secondary-200: #fed7aa;
  --secondary-300: #fdba74;
  --secondary-400: #fb923c;
  --secondary-500: #ff9e1b;
  /* Main brand orange */
  --secondary-600: #e68a00;
  --secondary-700: #c2710c;
  --secondary-800: #9a5a0c;
  --secondary-900: #7c4a0d;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Semantic Colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;

  /* Legacy color mappings for backward compatibility */
  --background: #ffffff;
  --foreground: var(--neutral-800);
  --primary-color: var(--primary-500);
  --secondary-color: var(--secondary-500);
  --accent-color: var(--primary-50);
  --light-gray: var(--neutral-50);
  --medium-gray: var(--neutral-200);
  --dark-gray: var(--neutral-500);

  /* ===== TYPOGRAPHY SYSTEM ===== */
  /* Font Families */
  --font-family-poppins-light: var(--font-poppins), 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-poppins-regular: var(--font-poppins), 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-poppins-medium: var(--font-poppins), 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-poppins-semibold: var(--font-poppins), 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-bauhaus-std-medium: var(--font-bauhaus-std-medium), 'Bauhaus 93', Impact, 'Arial Black', sans-serif;
  --font-family-bauhaus-pro: var(--font-bauhaus), 'Bauhaus 93', Impact, 'Arial Black', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;

  /* Font System */
  --font-sans: var(--font-family-poppins-regular);
  --font-display: var(--font-family-bauhaus-pro);
  --font-mono: var(--font-family-mono);

  /* Font Sizes - Mobile First */
  --font-size-xs: 0.75rem;
  /* 12px */
  --font-size-sm: 0.875rem;
  /* 14px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-md: 1.125rem;
  /* 18px */
  --font-size-lg: 1.25rem;
  /* 20px */
  --font-size-xl: 1.5rem;
  /* 24px */
  --font-size-2xl: 1.875rem;
  /* 30px */
  --font-size-3xl: 2.25rem;
  /* 36px */
  --font-size-4xl: 3rem;
  /* 48px */
  --font-size-5xl: 3.75rem;
  /* 60px */
  --font-size-6xl: 4.5rem;
  /* 72px */

  /* Font Weights */
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* ===== SPACING SYSTEM ===== */
  --space-px: 1px;
  --space-0: 0px;
  --space-0-5: 0.125rem;
  /* 2px */
  --space-1: 0.25rem;
  /* 4px */
  --space-1-5: 0.375rem;
  /* 6px */
  --space-2: 0.5rem;
  /* 8px */
  --space-2-5: 0.625rem;
  /* 10px */
  --space-3: 0.75rem;
  /* 12px */
  --space-3-5: 0.875rem;
  /* 14px */
  --space-4: 1rem;
  /* 16px */
  --space-5: 1.25rem;
  /* 20px */
  --space-6: 1.5rem;
  /* 24px */
  --space-7: 1.75rem;
  /* 28px */
  --space-8: 2rem;
  /* 32px */
  --space-9: 2.25rem;
  /* 36px */
  --space-10: 2.5rem;
  /* 40px */
  --space-11: 2.75rem;
  /* 44px */
  --space-12: 3rem;
  /* 48px */
  --space-14: 3.5rem;
  /* 56px */
  --space-16: 4rem;
  /* 64px */
  --space-20: 5rem;
  /* 80px */
  --space-24: 6rem;
  /* 96px */
  --space-28: 7rem;
  /* 112px */
  --space-32: 8rem;
  /* 128px */
  --space-36: 9rem;
  /* 144px */
  --space-40: 10rem;
  /* 160px */
  --space-44: 11rem;
  /* 176px */
  --space-48: 12rem;
  /* 192px */
  --space-52: 13rem;
  /* 208px */
  --space-56: 14rem;
  /* 224px */
  --space-60: 15rem;
  /* 240px */
  --space-64: 16rem;
  /* 256px */
  --space-72: 18rem;
  /* 288px */
  --space-80: 20rem;
  /* 320px */
  --space-96: 24rem;
  /* 384px */

  /* ===== BORDER SYSTEM ===== */
  --border-width-0: 0px;
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;

  --border-radius-none: 0px;
  --border-radius-sm: 0.125rem;
  /* 2px */
  --border-radius-base: 0.25rem;
  /* 4px */
  --border-radius-md: 0.375rem;
  /* 6px */
  --border-radius-lg: 0.5rem;
  /* 8px */
  --border-radius-xl: 0.75rem;
  /* 12px */
  --border-radius-2xl: 1rem;
  /* 16px */
  --border-radius-3xl: 1.5rem;
  /* 24px */
  --border-radius-full: 9999px;

  /* ===== SHADOW SYSTEM ===== */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* ===== ANIMATION SYSTEM ===== */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-colors: color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-opacity: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-shadow: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-transform: transform 150ms cubic-bezier(0.4, 0, 0.2, 1);

  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ===== Z-INDEX SYSTEM ===== */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* ===== BREAKPOINTS (for reference in media queries) ===== */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  /* Base font size for rem calculations */
}

body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-regular);
  line-height: var(--line-height-normal);
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  letter-spacing: var(--letter-spacing-tight);
  min-height: 100vh;
}

/* ===== TYPOGRAPHY STYLES ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: var(--line-height-tight);
  color: var(--foreground);
  margin-bottom: var(--space-4);
  font-weight: var(--font-semibold);
}

/* Display headings with Bauhaus Pro */
h1,
h2 {
  font-family: var(--font-display);
  font-weight: var(--font-medium);
  letter-spacing: var(--letter-spacing-tighter);
}

/* Content headings with Poppins */
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-poppins-semibold);
  font-weight: var(--font-semibold);
  letter-spacing: var(--letter-spacing-tight);
}

/* Responsive heading sizes */
h1 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-6);
}

h2 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-5);
}

h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-4);
}

h4 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-3);
}

h5 {
  font-size: var(--font-size-md);
  margin-bottom: var(--space-3);
}

h6 {
  font-size: var(--font-size-base);
  margin-bottom: var(--space-2);
}

/* Responsive typography for larger screens */
@media (min-width: 768px) {
  h1 {
    font-size: var(--font-size-4xl);
  }

  h2 {
    font-size: var(--font-size-3xl);
  }

  h3 {
    font-size: var(--font-size-2xl);
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: var(--font-size-5xl);
  }

  h2 {
    font-size: var(--font-size-4xl);
  }

  h3 {
    font-size: var(--font-size-3xl);
  }
}

/* Body text */
p {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4);
  color: var(--neutral-700);
}

/* Text utilities */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.font-light {
  font-weight: var(--font-light);
}

.font-normal {
  font-weight: var(--font-regular);
}

.font-medium {
  font-weight: var(--font-medium);
}

.font-semibold {
  font-weight: var(--font-semibold);
}

.font-bold {
  font-weight: var(--font-bold);
}

.leading-tight {
  line-height: var(--line-height-tight);
}

.leading-normal {
  line-height: var(--line-height-normal);
}

.leading-relaxed {
  line-height: var(--line-height-relaxed);
}

/* Text colors */
.text-primary {
  color: var(--primary-500);
}

.text-secondary {
  color: var(--secondary-500);
}

.text-success {
  color: var(--success-500);
}

.text-error {
  color: var(--error-500);
}

.text-warning {
  color: var(--warning-500);
}

.text-info {
  color: var(--info-500);
}

.text-muted {
  color: var(--neutral-500);
}

/* Ensure consistent font family */
span,
a,
button,
input,
select,
textarea,
label {
  font-family: var(--font-sans);
}

/* ===== LINKS ===== */
a {
  color: var(--primary-500);
  text-decoration: none;
  transition: var(--transition-colors);
}

a:hover {
  color: var(--primary-600);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* ===== BUTTON SYSTEM ===== */
button {
  font-family: var(--font-sans);
  font-weight: var(--font-medium);
  font-size: var(--font-size-base);
  cursor: pointer;
  border: none;
  border-radius: var(--border-radius-lg);
  padding: var(--space-3) var(--space-6);
  transition: var(--transition-all);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  text-align: center;
  line-height: 1;
  position: relative;
  overflow: hidden;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Primary Button */
.btn-primary {
  background-color: var(--primary-500);
  color: white;
  font-family: var(--font-display);
  font-weight: var(--font-medium);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
  background-color: var(--secondary-500);
  color: white;
  font-family: var(--font-family-poppins-semibold);
  font-weight: var(--font-semibold);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--secondary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Outline Button */
.btn-outline {
  background-color: transparent;
  color: var(--primary-500);
  border: var(--border-width-2) solid var(--primary-500);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-500);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Ghost Button */
.btn-ghost {
  background-color: transparent;
  color: var(--primary-500);
  border: none;
  box-shadow: none;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--primary-50);
  color: var(--primary-600);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-md);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-xl);
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--font-size-xl);
  border-radius: var(--border-radius-2xl);
}

/* Button States */
.btn-success {
  background-color: var(--success-500);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: var(--success-600);
}

.btn-error {
  background-color: var(--error-500);
  color: white;
}

.btn-error:hover:not(:disabled) {
  background-color: var(--error-600);
}

.btn-warning {
  background-color: var(--warning-500);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--warning-600);
}

/* ===== FORM CONTROLS ===== */
input,
select,
textarea {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-regular);
  line-height: var(--line-height-normal);
  color: var(--neutral-800);
  background-color: var(--background);
  border: var(--border-width-1) solid var(--neutral-300);
  border-radius: var(--border-radius-lg);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-all);
  width: 100%;
}

input:focus,
select:focus,
textarea:focus {
  border-color: var(--primary-500);
  outline: none;
  box-shadow: 0 0 0 3px rgba(10, 94, 178, 0.1);
  background-color: var(--primary-50);
}

input:hover:not(:focus),
select:hover:not(:focus),
textarea:hover:not(:focus) {
  border-color: var(--neutral-400);
}

input::placeholder,
textarea::placeholder {
  color: var(--neutral-500);
  font-weight: var(--font-regular);
}

/* Input States */
input:invalid,
select:invalid,
textarea:invalid {
  border-color: var(--error-500);
}

input:invalid:focus,
select:invalid:focus,
textarea:invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  background-color: var(--error-50);
}

input:disabled,
select:disabled,
textarea:disabled {
  background-color: var(--neutral-100);
  border-color: var(--neutral-200);
  color: var(--neutral-500);
  cursor: not-allowed;
}

/* Input Sizes */
.input-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-md);
}

.input-lg {
  padding: var(--space-4) var(--space-5);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-xl);
}

/* Textarea specific */
textarea {
  resize: vertical;
  min-height: 120px;
}

/* Select specific */
select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--space-10);
  appearance: none;
}

/* Checkbox and Radio */
input[type="checkbox"],
input[type="radio"] {
  width: auto;
  margin-right: var(--space-2);
  accent-color: var(--primary-500);
}

/* Labels */
label {
  display: block;
  font-weight: var(--font-medium);
  color: var(--neutral-700);
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
}

/* Form Groups */
.form-group {
  margin-bottom: var(--space-6);
}

.form-group label {
  margin-bottom: var(--space-2);
}

/* Error Messages */
.form-error {
  color: var(--error-500);
  font-size: var(--font-size-sm);
  margin-top: var(--space-1);
  display: block;
}

/* Success Messages */
.form-success {
  color: var(--success-500);
  font-size: var(--font-size-sm);
  margin-top: var(--space-1);
  display: block;
}

/* Help Text */
.form-help {
  color: var(--neutral-500);
  font-size: var(--font-size-sm);
  margin-top: var(--space-1);
  display: block;
}

/* ===== UTILITY CLASSES ===== */

/* Layout Utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

/* Grid Utilities */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.gap-1 {
  gap: var(--space-1);
}

.gap-2 {
  gap: var(--space-2);
}

.gap-3 {
  gap: var(--space-3);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.gap-8 {
  gap: var(--space-8);
}

/* Spacing Utilities */
.m-0 {
  margin: 0;
}

.m-1 {
  margin: var(--space-1);
}

.m-2 {
  margin: var(--space-2);
}

.m-3 {
  margin: var(--space-3);
}

.m-4 {
  margin: var(--space-4);
}

.m-6 {
  margin: var(--space-6);
}

.m-8 {
  margin: var(--space-8);
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: var(--space-1);
}

.mt-2 {
  margin-top: var(--space-2);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-6 {
  margin-top: var(--space-6);
}

.mt-8 {
  margin-top: var(--space-8);
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: var(--space-1);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: var(--space-1);
}

.p-2 {
  padding: var(--space-2);
}

.p-3 {
  padding: var(--space-3);
}

.p-4 {
  padding: var(--space-4);
}

.p-6 {
  padding: var(--space-6);
}

.p-8 {
  padding: var(--space-8);
}

/* Background Colors */
.bg-primary {
  background-color: var(--primary-500);
}

.bg-primary-light {
  background-color: var(--primary-50);
}

.bg-secondary {
  background-color: var(--secondary-500);
}

.bg-white {
  background-color: white;
}

.bg-gray-50 {
  background-color: var(--neutral-50);
}

.bg-gray-100 {
  background-color: var(--neutral-100);
}

.bg-gray-200 {
  background-color: var(--neutral-200);
}

/* Border Utilities */
.border {
  border: var(--border-width-1) solid var(--neutral-200);
}

.border-t {
  border-top: var(--border-width-1) solid var(--neutral-200);
}

.border-b {
  border-bottom: var(--border-width-1) solid var(--neutral-200);
}

.border-primary {
  border-color: var(--primary-500);
}

.rounded {
  border-radius: var(--border-radius-base);
}

.rounded-md {
  border-radius: var(--border-radius-md);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.rounded-xl {
  border-radius: var(--border-radius-xl);
}

.rounded-full {
  border-radius: var(--border-radius-full);
}

/* Shadow Utilities */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow {
  box-shadow: var(--shadow-base);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* Position Utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* Display Utilities */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.hidden {
  display: none;
}

/* Width & Height */
.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.h-full {
  height: 100%;
}

.h-auto {
  height: auto;
}

.min-h-screen {
  min-height: 100vh;
}

/* Text Alignment */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* Overflow */
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* Transitions */
.transition {
  transition: var(--transition-all);
}

.transition-colors {
  transition: var(--transition-colors);
}

.transition-transform {
  transition: var(--transition-transform);
}

/* Transform */
.transform {
  transform: translateZ(0);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.hover\:-translate-y-1:hover {
  transform: translateY(-0.25rem);
}

/* ===== COMPONENT STYLES ===== */

/* Card Component */
.card {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  padding: var(--space-6);
  border: var(--border-width-1) solid var(--neutral-100);
  transition: var(--transition-all);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: var(--border-width-1) solid var(--neutral-100);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
}

.card-body {
  color: var(--neutral-600);
}

/* Badge Component */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

.badge-success {
  background-color: var(--success-50);
  color: var(--success-600);
}

.badge-error {
  background-color: var(--error-50);
  color: var(--error-600);
}

.badge-warning {
  background-color: var(--warning-50);
  color: var(--warning-600);
}

/* Alert Component */
.alert {
  padding: var(--space-4);
  border-radius: var(--border-radius-lg);
  border: var(--border-width-1) solid;
  margin-bottom: var(--space-4);
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-700);
}

.alert-error {
  background-color: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-700);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-700);
}

.alert-info {
  background-color: var(--info-50);
  border-color: var(--info-200);
  color: var(--info-700);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile First Responsive Utilities */
@media (min-width: 640px) {
  .sm\:text-lg {
    font-size: var(--font-size-lg);
  }

  .sm\:text-xl {
    font-size: var(--font-size-xl);
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:p-6 {
    padding: var(--space-6);
  }

  .sm\:p-8 {
    padding: var(--space-8);
  }
}

@media (min-width: 768px) {
  .md\:text-xl {
    font-size: var(--font-size-xl);
  }

  .md\:text-2xl {
    font-size: var(--font-size-2xl);
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:p-8 {
    padding: var(--space-8);
  }

  .md\:p-12 {
    padding: var(--space-12);
  }
}

@media (min-width: 1024px) {
  .lg\:text-2xl {
    font-size: var(--font-size-2xl);
  }

  .lg\:text-3xl {
    font-size: var(--font-size-3xl);
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:p-12 {
    padding: var(--space-12);
  }

  .lg\:p-16 {
    padding: var(--space-16);
  }
}

/* ===== ACCESSIBILITY ===== */

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* Remove focus outline for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-500: #0066cc;
    --secondary-500: #ff6600;
    --neutral-800: #000000;
    --neutral-600: #333333;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-500);
  color: white;
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  z-index: var(--z-toast);
  transition: var(--transition-all);
}

.skip-link:focus {
  top: 6px;
}

/* ===== RTL SUPPORT ===== */

[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  direction: rtl;
}

/* RTL specific adjustments */
[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
  text-align: right;
}

[dir="rtl"] button {
  text-align: center;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* ===== DARK MODE SUPPORT ===== */

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--neutral-900);
    --foreground: var(--neutral-100);
    --primary-50: #1e3a8a;
    --primary-100: #1e40af;
    --neutral-50: var(--neutral-800);
    --neutral-100: var(--neutral-700);
    --neutral-200: var(--neutral-600);
  }

  .card {
    background: var(--neutral-800);
    border-color: var(--neutral-700);
  }

  input,
  select,
  textarea {
    background-color: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-100);
  }

  input:focus,
  select:focus,
  textarea:focus {
    background-color: var(--neutral-700);
  }
}

/* Force light mode class */
.light-mode {
  --background: #ffffff;
  --foreground: var(--neutral-800);
}

/* Force dark mode class */
.dark-mode {
  --background: var(--neutral-900);
  --foreground: var(--neutral-100);
}

/* ===== PRINT STYLES ===== */

@media print {

  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  abbr[title]::after {
    content: " (" attr(title) ")";
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  .no-print {
    display: none !important;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* GPU acceleration for animations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize font loading - this is a general rule for all @font-face declarations */
/* Individual font files should include: font-display: swap; */

/* Optimize images */
img {
  max-width: 100%;
  height: auto;
}

/* Lazy loading support */
img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
  opacity: 1;
}